import Link from "next/link"
import { Facebook, Twitter, Instagram, Youtube } from "lucide-react"

export function Footer() {
  return (
    <footer className="bg-slate-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-white to-slate-300" />
              <span className="text-xl font-bold">ModernStore</span>
            </div>
            <p className="text-slate-300">
              Experience the future of online shopping with immersive 3D product views and premium quality products.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-slate-400 hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-slate-400 hover:text-white transition-colors">
                <Twitter className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-slate-400 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-slate-400 hover:text-white transition-colors">
                <Youtube className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Links</h3>
            <div className="space-y-2">
              <Link href="/products" className="block text-slate-300 hover:text-white transition-colors">
                Products
              </Link>
              <Link href="/categories" className="block text-slate-300 hover:text-white transition-colors">
                Categories
              </Link>
              <Link href="/about" className="block text-slate-300 hover:text-white transition-colors">
                About Us
              </Link>
              <Link href="/contact" className="block text-slate-300 hover:text-white transition-colors">
                Contact
              </Link>
            </div>
          </div>

          {/* Customer Service */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Customer Service</h3>
            <div className="space-y-2">
              <Link href="/help" className="block text-slate-300 hover:text-white transition-colors">
                Help Center
              </Link>
              <Link href="/shipping" className="block text-slate-300 hover:text-white transition-colors">
                Shipping Info
              </Link>
              <Link href="/returns" className="block text-slate-300 hover:text-white transition-colors">
                Returns
              </Link>
              <Link href="/track" className="block text-slate-300 hover:text-white transition-colors">
                Track Order
              </Link>
            </div>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Legal</h3>
            <div className="space-y-2">
              <Link href="/privacy" className="block text-slate-300 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="block text-slate-300 hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link href="/cookies" className="block text-slate-300 hover:text-white transition-colors">
                Cookie Policy
              </Link>
              <Link href="/accessibility" className="block text-slate-300 hover:text-white transition-colors">
                Accessibility
              </Link>
            </div>
          </div>
        </div>

        <div className="border-t border-slate-800 mt-12 pt-8 text-center">
          <p className="text-slate-400">
            © 2024 ModernStore. All rights reserved. Built with ❤️ for the future of eCommerce.
          </p>
        </div>
      </div>
    </footer>
  )
}
