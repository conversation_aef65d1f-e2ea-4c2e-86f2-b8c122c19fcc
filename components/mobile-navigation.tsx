"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { useCart } from "@/components/cart-context"
import { useWishlist } from "@/components/wishlist-context"
import { 
  Home, 
  Search, 
  ShoppingBag, 
  Heart, 
  User,
  Grid3X3,
  Bell
} from "lucide-react"
import { cn } from "@/lib/utils"

interface NavItem {
  href: string
  icon: React.ComponentType<{ className?: string }>
  label: string
  badge?: number
}

export function MobileNavigation() {
  const pathname = usePathname()
  const { items } = useCart()
  const { items: wishlistItems } = useWishlist()
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)

  // Auto-hide navigation on scroll down, show on scroll up
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false) // Hide when scrolling down
      } else {
        setIsVisible(true) // Show when scrolling up
      }
      
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  const navItems: NavItem[] = [
    {
      href: "/",
      icon: Home,
      label: "Home"
    },
    {
      href: "/products",
      icon: Search,
      label: "Browse"
    },
    {
      href: "/categories",
      icon: Grid3X3,
      label: "Categories"
    },
    {
      href: "/wishlist",
      icon: Heart,
      label: "Wishlist",
      badge: wishlistItems.length
    },
    {
      href: "/cart",
      icon: ShoppingBag,
      label: "Cart",
      badge: items.reduce((sum, item) => sum + item.quantity, 0)
    }
  ]

  return (
    <>
      {/* Bottom Navigation Bar */}
      <nav 
        className={cn(
          "fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-slate-200 md:hidden transition-transform duration-300",
          isVisible ? "translate-y-0" : "translate-y-full"
        )}
      >
        <div className="grid grid-cols-5 h-16">
          {navItems.map((item) => {
            const isActive = pathname === item.href || 
                           (item.href !== "/" && pathname.startsWith(item.href))
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex flex-col items-center justify-center gap-1 text-xs font-medium transition-colors relative",
                  isActive 
                    ? "text-slate-900 bg-slate-50" 
                    : "text-slate-600 hover:text-slate-900"
                )}
              >
                <div className="relative">
                  <item.icon className="h-5 w-5" />
                  {item.badge && item.badge > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-2 -right-2 h-4 w-4 p-0 flex items-center justify-center text-xs"
                    >
                      {item.badge > 99 ? "99+" : item.badge}
                    </Badge>
                  )}
                </div>
                <span className="leading-none">{item.label}</span>
                
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-slate-900 rounded-full" />
                )}
              </Link>
            )
          })}
        </div>
      </nav>

      {/* Spacer to prevent content from being hidden behind the nav */}
      <div className="h-16 md:hidden" />
    </>
  )
}

// Floating Action Button for quick actions
export function FloatingActionButton() {
  const [isExpanded, setIsExpanded] = useState(false)
  const pathname = usePathname()

  // Don't show on certain pages
  if (pathname === "/cart" || pathname === "/checkout") {
    return null
  }

  const quickActions = [
    {
      href: "/products",
      icon: Search,
      label: "Search",
      color: "bg-blue-500 hover:bg-blue-600"
    },
    {
      href: "/cart",
      icon: ShoppingBag,
      label: "Cart",
      color: "bg-green-500 hover:bg-green-600"
    },
    {
      href: "/wishlist",
      icon: Heart,
      label: "Wishlist",
      color: "bg-red-500 hover:bg-red-600"
    }
  ]

  return (
    <div className="fixed bottom-20 right-4 z-40 md:hidden">
      {/* Quick Action Buttons */}
      {isExpanded && (
        <div className="flex flex-col gap-2 mb-2">
          {quickActions.map((action, index) => (
            <Link
              key={action.href}
              href={action.href}
              className={cn(
                "w-12 h-12 rounded-full text-white shadow-lg flex items-center justify-center transition-all duration-200",
                action.color,
                "animate-in slide-in-from-bottom-2 fade-in-0"
              )}
              style={{ animationDelay: `${index * 50}ms` }}
              onClick={() => setIsExpanded(false)}
            >
              <action.icon className="h-5 w-5" />
            </Link>
          ))}
        </div>
      )}

      {/* Main FAB */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={cn(
          "w-14 h-14 bg-slate-900 hover:bg-slate-800 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200",
          isExpanded && "rotate-45"
        )}
      >
        {isExpanded ? (
          <div className="w-6 h-0.5 bg-white" />
        ) : (
          <div className="relative">
            <div className="w-6 h-0.5 bg-white" />
            <div className="w-6 h-0.5 bg-white absolute top-0 rotate-90" />
          </div>
        )}
      </button>
    </div>
  )
}

// Hook for mobile navigation state
export function useMobileNavigation() {
  const [isVisible, setIsVisible] = useState(true)
  const [isAtBottom, setIsAtBottom] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollHeight = document.documentElement.scrollHeight
      const clientHeight = document.documentElement.clientHeight
      
      setIsAtBottom(scrollTop + clientHeight >= scrollHeight - 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return { isVisible, isAtBottom }
}
