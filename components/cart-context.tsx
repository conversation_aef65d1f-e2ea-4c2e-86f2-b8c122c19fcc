"use client"

import { createContext, useContext, useMemo, useState, useEffect, ReactNode } from "react"
import type { Product } from "@/lib/types"

export interface CartLineItem {
  productId: string
  quantity: number
  price: number
  name: string
  image?: string
  slug: string
}

interface CartState {
  items: CartLineItem[]
}

interface CartContextValue {
  items: CartLineItem[]
  itemCount: number
  subtotal: number
  addItem: (product: Product, quantity?: number) => void
  removeItem: (productId: string) => void
  updateQuantity: (productId: string, quantity: number) => void
  clear: () => void
}

const CartContext = createContext<CartContextValue | undefined>(undefined)

const STORAGE_KEY = "modernstore.cart.v1"

export function CartProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<CartState>({ items: [] })

  useEffect(() => {
    try {
      const raw = typeof window !== "undefined" ? window.localStorage.getItem(STORAGE_KEY) : null
      if (raw) {
        const parsed = JSON.parse(raw) as CartState
        setState(parsed)
      }
    } catch {}
  }, [])

  useEffect(() => {
    try {
      if (typeof window !== "undefined") {
        window.localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
      }
    } catch {}
  }, [state])

  const value = useMemo<CartContextValue>(() => {
    const addItem = (product: Product, quantity = 1) => {
      setState((prev) => {
        const existing = prev.items.find((it) => it.productId === product.id)
        if (existing) {
          return {
            items: prev.items.map((it) =>
              it.productId === product.id ? { ...it, quantity: it.quantity + quantity } : it
            ),
          }
        }
        const line: CartLineItem = {
          productId: product.id,
          quantity,
          price: product.price,
          name: product.name,
          image: product.images?.[0],
          slug: product.slug,
        }
        return { items: [...prev.items, line] }
      })
    }

    const removeItem = (productId: string) => {
      setState((prev) => ({ items: prev.items.filter((it) => it.productId !== productId) }))
    }

    const updateQuantity = (productId: string, quantity: number) => {
      setState((prev) => {
        if (quantity <= 0) {
          return { items: prev.items.filter((it) => it.productId !== productId) }
        }
        return {
          items: prev.items.map((it) => (it.productId === productId ? { ...it, quantity } : it)),
        }
      })
    }

    const clear = () => setState({ items: [] })

    const itemCount = state.items.reduce((sum, it) => sum + it.quantity, 0)
    const subtotal = state.items.reduce((sum, it) => sum + it.price * it.quantity, 0)

    return { items: state.items, itemCount, subtotal, addItem, removeItem, updateQuantity, clear }
  }, [state])

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>
}

export function useCart() {
  const ctx = useContext(CartContext)
  if (!ctx) throw new Error("useCart must be used within a CartProvider")
  return ctx
}



