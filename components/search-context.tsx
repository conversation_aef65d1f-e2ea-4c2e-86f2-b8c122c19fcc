"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { mockProducts } from "@/lib/mock-data"
import type { Product } from "@/lib/types"

interface SearchFilters {
  query: string
  category: string
  priceRange: [number, number]
  sortBy: "name" | "price-low" | "price-high" | "newest" | "featured"
  inStock: boolean
}

interface SearchContextValue {
  filters: SearchFilters
  filteredProducts: Product[]
  updateFilters: (newFilters: Partial<SearchFilters>) => void
  resetFilters: () => void
  isLoading: boolean
}

const SearchContext = createContext<SearchContextValue | undefined>(undefined)

const defaultFilters: SearchFilters = {
  query: "",
  category: "",
  priceRange: [0, 1000],
  sortBy: "featured",
  inStock: false,
}

export function SearchProvider({ children }: { children: ReactNode }) {
  const [filters, setFilters] = useState<SearchFilters>(defaultFilters)
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(mockProducts)
  const [isLoading, setIsLoading] = useState(false)

  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const resetFilters = () => {
    setFilters(defaultFilters)
  }

  useEffect(() => {
    setIsLoading(true)
    
    // Simulate API delay
    const timeoutId = setTimeout(() => {
      let results = [...mockProducts]

      // Filter by search query
      if (filters.query.trim()) {
        const query = filters.query.toLowerCase()
        results = results.filter(product =>
          product.name.toLowerCase().includes(query) ||
          product.description.toLowerCase().includes(query) ||
          product.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }

      // Filter by category
      if (filters.category) {
        results = results.filter(product => product.category.slug === filters.category)
      }

      // Filter by price range
      results = results.filter(product =>
        product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]
      )

      // Filter by stock
      if (filters.inStock) {
        results = results.filter(product => product.inventory > 0)
      }

      // Sort results
      switch (filters.sortBy) {
        case "name":
          results.sort((a, b) => a.name.localeCompare(b.name))
          break
        case "price-low":
          results.sort((a, b) => a.price - b.price)
          break
        case "price-high":
          results.sort((a, b) => b.price - a.price)
          break
        case "newest":
          results.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          break
        case "featured":
          results.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0))
          break
      }

      setFilteredProducts(results)
      setIsLoading(false)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [filters])

  const value = {
    filters,
    filteredProducts,
    updateFilters,
    resetFilters,
    isLoading,
  }

  return <SearchContext.Provider value={value}>{children}</SearchContext.Provider>
}

export function useSearch() {
  const context = useContext(SearchContext)
  if (context === undefined) {
    throw new Error("useSearch must be used within a SearchProvider")
  }
  return context
}
