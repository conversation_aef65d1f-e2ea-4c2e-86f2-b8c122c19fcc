"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star, ThumbsUp, ThumbsDown, Flag, Send } from "lucide-react"
import { mockReviews } from "@/lib/mock-data"
import { toast } from "sonner"
import type { Review } from "@/lib/types"

interface ProductReviewsProps {
  productId: string
  className?: string
}

interface ReviewFormData {
  rating: number
  title: string
  content: string
  name: string
  email: string
}

export function ProductReviews({ productId, className = "" }: ProductReviewsProps) {
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [reviews, setReviews] = useState<Review[]>(
    mockReviews.filter(review => review.productId === productId)
  )
  const [formData, setFormData] = useState<ReviewFormData>({
    rating: 0,
    title: "",
    content: "",
    name: "",
    email: ""
  })
  const [hoveredRating, setHoveredRating] = useState(0)

  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0

  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(review => review.rating === rating).length,
    percentage: reviews.length > 0 
      ? (reviews.filter(review => review.rating === rating).length / reviews.length) * 100 
      : 0
  }))

  const handleSubmitReview = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (formData.rating === 0) {
      toast.error("Please select a rating")
      return
    }

    const newReview: Review = {
      id: Date.now().toString(),
      productId,
      userId: "temp-user",
      user: {
        id: "temp-user",
        name: formData.name,
        email: formData.email,
        role: "customer",
        createdAt: new Date(),
        updatedAt: new Date()
      },
      rating: formData.rating,
      title: formData.title,
      content: formData.content,
      verified: false,
      createdAt: new Date()
    }

    setReviews(prev => [newReview, ...prev])
    setFormData({ rating: 0, title: "", content: "", name: "", email: "" })
    setShowReviewForm(false)
    toast.success("Review submitted successfully!", {
      description: "Thank you for your feedback. Your review is pending approval."
    })
  }

  const handleInputChange = (field: keyof ReviewFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date)
  }

  return (
    <div className={className}>
      {/* Reviews Summary */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Customer Reviews</span>
            <Button 
              onClick={() => setShowReviewForm(!showReviewForm)}
              className="bg-slate-900 hover:bg-slate-800"
            >
              Write a Review
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-8">
            {/* Overall Rating */}
            <div className="text-center">
              <div className="text-4xl font-bold text-slate-900 mb-2">
                {averageRating.toFixed(1)}
              </div>
              <div className="flex items-center justify-center mb-2">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${
                      i < Math.floor(averageRating) 
                        ? "text-yellow-400 fill-current" 
                        : "text-slate-300"
                    }`}
                  />
                ))}
              </div>
              <div className="text-sm text-slate-600">
                Based on {reviews.length} review{reviews.length !== 1 ? 's' : ''}
              </div>
            </div>

            {/* Rating Distribution */}
            <div className="space-y-2">
              {ratingDistribution.map(({ rating, count, percentage }) => (
                <div key={rating} className="flex items-center gap-2 text-sm">
                  <span className="w-8">{rating}★</span>
                  <div className="flex-1 bg-slate-200 rounded-full h-2">
                    <div 
                      className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <span className="w-8 text-slate-600">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Review Form */}
      {showReviewForm && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Write a Review</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmitReview} className="space-y-4">
              {/* Rating */}
              <div>
                <Label className="text-sm font-medium">Rating *</Label>
                <div className="flex items-center gap-1 mt-1">
                  {[1, 2, 3, 4, 5].map((rating) => (
                    <button
                      key={rating}
                      type="button"
                      className="p-1"
                      onMouseEnter={() => setHoveredRating(rating)}
                      onMouseLeave={() => setHoveredRating(0)}
                      onClick={() => handleInputChange('rating', rating)}
                    >
                      <Star
                        className={`h-6 w-6 transition-colors ${
                          rating <= (hoveredRating || formData.rating)
                            ? "text-yellow-400 fill-current"
                            : "text-slate-300"
                        }`}
                      />
                    </button>
                  ))}
                </div>
              </div>

              {/* Title */}
              <div>
                <Label htmlFor="review-title">Review Title *</Label>
                <Input
                  id="review-title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Summarize your experience"
                  required
                  className="mt-1"
                />
              </div>

              {/* Content */}
              <div>
                <Label htmlFor="review-content">Review *</Label>
                <Textarea
                  id="review-content"
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  placeholder="Share your thoughts about this product..."
                  required
                  rows={4}
                  className="mt-1"
                />
              </div>

              {/* Name and Email */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="reviewer-name">Name *</Label>
                  <Input
                    id="reviewer-name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="reviewer-email">Email *</Label>
                  <Input
                    id="reviewer-email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit" className="bg-slate-900 hover:bg-slate-800">
                  <Send className="h-4 w-4 mr-2" />
                  Submit Review
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowReviewForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-slate-600">No reviews yet. Be the first to review this product!</p>
            </CardContent>
          </Card>
        ) : (
          reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src={review.user.avatar} />
                      <AvatarFallback>
                        {review.user.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{review.user.name}</span>
                        {review.verified && (
                          <Badge variant="secondary" className="text-xs">
                            Verified Purchase
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < review.rating 
                                  ? "text-yellow-400 fill-current" 
                                  : "text-slate-300"
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-slate-600">
                          {formatDate(review.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Flag className="h-4 w-4" />
                  </Button>
                </div>

                <h4 className="font-medium text-slate-900 mb-2">{review.title}</h4>
                <p className="text-slate-700 mb-4">{review.content}</p>

                <div className="flex items-center gap-4 text-sm">
                  <Button variant="ghost" size="sm" className="text-slate-600">
                    <ThumbsUp className="h-4 w-4 mr-1" />
                    Helpful (0)
                  </Button>
                  <Button variant="ghost" size="sm" className="text-slate-600">
                    <ThumbsDown className="h-4 w-4 mr-1" />
                    Not Helpful (0)
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
