"use client"

import Link from "next/link"
import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, ShoppingCart, Eye } from "lucide-react"
import { mockProducts } from "@/lib/mock-data"
import { useCart } from "@/components/cart-context"
import { toast } from "sonner"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"
import type { CarouselApi } from "@/components/ui/carousel"

export function FeaturedProducts() {
  const { addItem } = useCart()
  const featuredProducts = mockProducts.slice(0, 8)
  const [carouselApi, setCarouselApi] = useState<CarouselApi | null>(null)

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">Featured Products</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Discover our handpicked selection of premium products with immersive 3D experiences
          </p>
        </div>

        <div className="relative">
          <Carousel
            opts={{ align: "start", loop: true, dragFree: true }}
            plugins={[
              Autoplay({ delay: 3000, stopOnInteraction: true, stopOnMouseEnter: true }),
            ]}
            setApi={(api) => setCarouselApi(api)}
          >
            <CarouselContent>
              {featuredProducts.map((product) => (
                <CarouselItem key={product.id} className="basis-full sm:basis-1/2 lg:basis-1/4">
                  <Card className="group hover:shadow-lg transition-shadow duration-300">
                    <CardContent className="p-0">
                      <div className="relative overflow-hidden rounded-t-lg">
                        <Link href={`/products/${product.slug}`}>
                          <img
                            src={product.images[0] || "/placeholder.svg"}
                            alt={product.name}
                            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </Link>
                        {product.compareAtPrice && product.compareAtPrice > product.price && (
                          <Badge className="absolute top-2 left-2 bg-red-500 hover:bg-red-600">
                            -{Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100)}%
                          </Badge>
                        )}
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Link href={`/products/${product.slug}`}>
                            <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                      </div>

                      <div className="p-4">
                        <Link href={`/products/${product.slug}`} className="block">
                          <h3 className="font-semibold text-slate-900 mb-2 line-clamp-2">{product.name}</h3>
                        </Link>

                        <div className="flex items-center mb-2">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${i < 4 ? "text-yellow-400 fill-current" : "text-slate-300"}`}
                              />
                            ))}
                          </div>
                          <span className="text-sm text-slate-600 ml-2">({Math.floor(Math.random() * 50) + 10})</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold text-slate-900">${product.price.toFixed(2)}</span>
                            {product.compareAtPrice && product.compareAtPrice > product.price && (
                              <span className="text-sm text-slate-500 line-through">
                                ${product.compareAtPrice.toFixed(2)}
                              </span>
                            )}
                          </div>
                          <Button
                            size="sm"
                            className="bg-slate-900 hover:bg-slate-800"
                            onClick={() => {
                              addItem(product, 1)
                              toast.success("Added to cart", { description: product.name })
                            }}
                          >
                            <ShoppingCart className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
            {/* Pagination dots */}
            <CarouselDots api={carouselApi} />
            <CarouselPrevious className="hidden md:flex" />
            <CarouselNext className="hidden md:flex" />
          </Carousel>
        </div>

        <div className="text-center mt-12">
          <Link href="/products">
            <Button variant="outline" size="lg" className="border-slate-300 bg-transparent">
              View All Products
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}

// Local pagination dots component wired to Embla API via parent state
function CarouselDots({ api }: { api: CarouselApi | null }) {
  const [slideCount, setSlideCount] = useState(0)
  const [selectedIndex, setSelectedIndex] = useState(0)

  useEffect(() => {
    if (!api) return
    const onSelect = () => setSelectedIndex(api.selectedScrollSnap())
    setSlideCount(api.scrollSnapList().length)
    onSelect()
    api.on("select", onSelect)
    api.on("reInit", onSelect)
    return () => {
      api.off("select", onSelect)
      api.off("reInit", onSelect)
    }
  }, [api])

  if (!api || slideCount <= 1) return null

  return (
    <div className="mt-6 flex justify-center gap-2">
      {Array.from({ length: slideCount }).map((_, index) => (
        <button
          key={index}
          aria-label={`Go to slide ${index + 1}`}
          className={`h-2 w-2 rounded-full transition-colors ${
            index === selectedIndex ? "bg-slate-900" : "bg-slate-300 hover:bg-slate-400"
          }`}
          onClick={() => api && api.scrollTo(index)}
        />
      ))}
    </div>
  )
}
