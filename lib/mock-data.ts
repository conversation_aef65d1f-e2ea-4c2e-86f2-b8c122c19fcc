import type { Product, Category, User, Review } from "./types"

export const mockCategories: Category[] = [
  {
    id: "1",
    name: "Electronics",
    slug: "electronics",
    description: "Latest gadgets and electronic devices",
    image: "/electronics-category.png",
  },
  {
    id: "2",
    name: "Fashion",
    slug: "fashion",
    description: "Trendy clothing and accessories",
    image: "/fashion-category.png",
  },
  {
    id: "3",
    name: "Home & Garden",
    slug: "home-garden",
    description: "Everything for your home and garden",
    image: "/home-garden-category.png",
  },
]

export const mockProducts: Product[] = [
  {
    id: "1",
    name: "Wireless Headphones Pro",
    slug: "wireless-headphones-pro",
    description: "Premium wireless headphones with noise cancellation and superior sound quality.",
    price: 299.99,
    salePrice: 249.99,
    compareAtPrice: 399.99,
    rating: 4.8,
    images: ["/black-wireless-headphones.png", "/wireless-headphones-side.png", "/wireless-headphones-case.png"],
    model3d: "/models/headphones.glb",
    categoryId: "1",
    category: mockCategories[0],
    inventory: 50,
    sku: "WHP-001",
    weight: 0.3,
    dimensions: { length: 20, width: 18, height: 8 },
    tags: ["wireless", "audio", "premium", "noise-cancelling"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "2",
    name: "Smart Watch Series X",
    slug: "smart-watch-series-x",
    description: "Advanced smartwatch with health monitoring, GPS, and long battery life.",
    price: 449.99,
    rating: 4.6,
    images: ["/placeholder-h6szb.png", "/placeholder-5f6vv.png", "/placeholder-h2gu6.png"],
    model3d: "/models/smartwatch.glb",
    categoryId: "1",
    category: mockCategories[0],
    inventory: 30,
    sku: "SWX-001",
    weight: 0.05,
    dimensions: { length: 4, width: 4, height: 1 },
    tags: ["smartwatch", "fitness", "health", "gps"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-10"),
  },
  {
    id: "3",
    name: "Designer Leather Jacket",
    slug: "designer-leather-jacket",
    description: "Premium leather jacket with modern cut and exceptional craftsmanship.",
    price: 599.99,
    salePrice: 479.99,
    compareAtPrice: 799.99,
    rating: 4.9,
    images: ["/placeholder-5g3id.png", "/placeholder-wsigt.png", "/leather-jacket-detail.png"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 15,
    sku: "LJ-001",
    weight: 1.2,
    dimensions: { length: 60, width: 50, height: 5 },
    tags: ["leather", "jacket", "fashion", "premium"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-12"),
  },
  {
    id: "4",
    name: "Modern Coffee Table",
    slug: "modern-coffee-table",
    description: "Sleek modern coffee table with glass top and wooden base.",
    price: 399.99,
    rating: 4.3,
    images: ["/modern-coffee-table-glass.jpg", "/coffee-table-wooden-base.jpg", "/coffee-table-living-room.jpg"],
    model3d: "/models/coffee-table.glb",
    categoryId: "3",
    category: mockCategories[2],
    inventory: 8,
    sku: "CT-001",
    weight: 25,
    dimensions: { length: 120, width: 60, height: 45 },
    tags: ["furniture", "table", "modern", "glass"],
    featured: false,
    status: "active",
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-08"),
  },
]

export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    email: "<EMAIL>",
    name: "John Doe",
    role: "customer",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-05"),
  },
]

export const mockReviews: Review[] = [
  {
    id: "1",
    productId: "1",
    userId: "2",
    user: mockUsers[1],
    rating: 5,
    title: "Amazing sound quality!",
    content: "These headphones exceeded my expectations. The noise cancellation is incredible.",
    verified: true,
    createdAt: new Date("2024-01-20"),
  },
  {
    id: "2",
    productId: "2",
    userId: "2",
    user: mockUsers[1],
    rating: 4,
    title: "Great smartwatch",
    content: "Love the health features and battery life. Very comfortable to wear.",
    verified: true,
    createdAt: new Date("2024-01-18"),
  },
]
