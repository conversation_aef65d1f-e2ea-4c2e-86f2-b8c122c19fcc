"use client"
import Link from "next/link"
import { useEffect, useRef } from "react"
import { animateHoverScaleShadow, animateStaggerList } from "@/lib/anim"
import { Card, CardContent } from "@/components/ui/card"
import { mockCategories } from "@/lib/mock-data"

export default function CategoriesPage() {
  const categories = mockCategories
  const gridRef = useRef<HTMLDivElement | null>(null)

  return (
    <main className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="mb-12">
          <h1 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-2">Categories</h1>
          <p className="text-lg text-slate-600">Explore products by category</p>
        </div>

        <div ref={gridRef} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((cat) => (
            <Link key={cat.id} href={`/categories/${cat.slug}`}>
              <Card className="group transition-shadow" data-anim-card>
                <CardContent className="p-0">
                  <div className="aspect-[16/9] overflow-hidden rounded-t-lg">
                    <img src={cat.image || "/placeholder.svg"} alt={cat.name} className="w-full h-full object-cover" data-anim-img />
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-slate-900">{cat.name}</h3>
                    {cat.description ? <p className="text-sm text-slate-600 mt-1 line-clamp-2">{cat.description}</p> : null}
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
        <CategoriesPageAnimations gridRef={gridRef} />
      </div>
    </main>
  )
}

function CategoriesPageAnimations({ gridRef }: { gridRef: React.MutableRefObject<HTMLDivElement | null> }) {
  useEffect(() => {
    if (!gridRef.current) return
    const grid = gridRef.current
    const cards = grid.querySelectorAll<HTMLElement>("[data-anim-card]")
    const imgs = grid.querySelectorAll<HTMLElement>("[data-anim-img]")

    const prefersReduced = typeof window !== "undefined" && window.matchMedia("(prefers-reduced-motion: reduce)").matches
    const runEntrance = () => {
      if (cards.length) {
        if (prefersReduced) {
          cards.forEach((el) => {
            el.style.opacity = "1"
            el.style.transform = "none"
          })
        } else {
          animateStaggerList(cards)
        }
      }
    }
    const io = new IntersectionObserver((entries) => {
      const entry = entries[0]
      if (entry.isIntersecting) {
        io.disconnect()
        runEntrance()
      }
    }, { threshold: 0.2 })
    io.observe(grid)

    const cleanups: Array<() => void> = []
    cards.forEach((el) => cleanups.push(animateHoverScaleShadow(el)))
    imgs.forEach((el) => cleanups.push(animateHoverScaleShadow(el)))

    return () => {
      io.disconnect()
      cleanups.forEach((fn) => fn())
    }
  }, [gridRef])
  return null
}



