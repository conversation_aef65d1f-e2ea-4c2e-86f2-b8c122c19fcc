import Image from "next/image"
import { notFound } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { animateAddToCartClick } from "@/lib/anim"
import { Card, CardContent } from "@/components/ui/card"
import { mockProducts } from "@/lib/mock-data"

interface ProductPageProps {
  params: { slug: string }
}

export default function ProductDetailPage({ params }: ProductPageProps) {
  const product = mockProducts.find((p) => p.slug === params.slug)
  if (!product) return notFound()

  return (
    <main className="py-10">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-10 items-start">
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              {/* Using regular img due to images.unoptimized setting */}
              <img
                src={product.images[0] || "/placeholder.svg"}
                alt={product.name}
                className="w-full h-auto object-contain"
              />
            </CardContent>
          </Card>

          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-slate-900">{product.name}</h1>
              <p className="text-slate-600 mt-2">{product.description}</p>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-2xl font-bold text-slate-900">${product.price.toFixed(2)}</span>
              {product.compareAtPrice && product.compareAtPrice > product.price && (
                <span className="text-lg text-slate-500 line-through">${product.compareAtPrice.toFixed(2)}</span>
              )}
            </div>

            <CartButtons slug={product.slug} />

            <div className="text-sm text-slate-600">
              <div>SKU: {product.sku}</div>
              <div>Inventory: {product.inventory}</div>
              <div>Category: {product.category.name}</div>
              {product.tags?.length ? <div>Tags: {product.tags.join(", ")} </div> : null}
            </div>
          </div>
        </div>

        {product.images?.length > 1 ? (
          <div className="mt-10">
            <h2 className="text-xl font-semibold text-slate-900 mb-4">More Images</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {product.images.slice(1).map((src, idx) => (
                <img key={idx} src={src} alt={`${product.name} ${idx + 2}`} className="w-full h-40 object-cover" />
              ))}
            </div>
          </div>
        ) : null}
      </div>
    </main>
  )
}

function CartButtons({ slug }: { slug: string }) {
  "use client"
  const { useCart } = require("@/components/cart-context")
  const { addItem } = useCart()
  const { mockProducts } = require("@/lib/mock-data")
  const { toast } = require("sonner")
  const product = mockProducts.find((p: any) => p.slug === slug)
  if (!product) return null
  return (
    <div className="flex items-center gap-3">
      <Button className="bg-slate-900 hover:bg-slate-800" onClick={(e: any) => { animateAddToCartClick(e.currentTarget); addItem(product, 1); toast.success("Added to cart", { description: product.name }) }}>Add to Cart</Button>
      <Button variant="outline" onClick={() => { addItem(product, 1); toast.success("Added to cart", { description: product.name }) }}>Buy Now</Button>
    </div>
  )
}


