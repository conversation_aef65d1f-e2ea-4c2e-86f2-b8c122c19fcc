import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { GeistSans } from "geist/font/sans"
import { GeistMono } from "geist/font/mono"
import { Analytics } from "@vercel/analytics/next"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Suspense } from "react"
import { CartProvider } from "@/components/cart-context"
import { Toaster } from "@/components/ui/sonner"
import "./globals.css"

export const metadata: Metadata = {
  title: "ModernStore - Premium eCommerce Experience",
  description: "Discover premium products with immersive 3D views and seamless shopping experience",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable} antialiased`}>
        <CartProvider>
          <Suspense fallback={<div>Loading...</div>}>
            <Header />
            {children}
            <Footer />
          </Suspense>
          <Toaster position="bottom-right" richColors closeButton />
          <Analytics />
        </CartProvider>
      </body>
    </html>
  )
}
