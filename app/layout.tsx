import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON>stSans } from "geist/font/sans"
import { <PERSON>eist<PERSON><PERSON> } from "geist/font/mono"
import { Analytics } from "@vercel/analytics/next"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Suspense } from "react"
import { CartProvider } from "@/components/cart-context"
import { SearchProvider } from "@/components/search-context"
import { WishlistProvider } from "@/components/wishlist-context"
import { PerformanceMonitor } from "@/components/performance-monitor"
import { Toaster } from "@/components/ui/sonner"
import "./globals.css"

export const metadata: Metadata = {
  title: "ModernStore - Premium eCommerce Experience",
  description: "Discover premium products with immersive 3D views and seamless shopping experience",
  keywords: ["ecommerce", "online shopping", "premium products", "3D product view", "modern store"],
  authors: [{ name: "ModernStore Team" }],
  creator: "ModernStore",
  publisher: "ModernStore",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://modernstore.com",
    title: "ModernStore - Premium eCommerce Experience",
    description: "Discover premium products with immersive 3D views and seamless shopping experience",
    siteName: "ModernStore",
    images: [
      {
        url: "/3d-product-showcase-hero-image.jpg",
        width: 1200,
        height: 630,
        alt: "ModernStore - Premium eCommerce Experience",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "ModernStore - Premium eCommerce Experience",
    description: "Discover premium products with immersive 3D views and seamless shopping experience",
    images: ["/3d-product-showcase-hero-image.jpg"],
    creator: "@modernstore",
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable} antialiased`}>
        <SearchProvider>
          <WishlistProvider>
            <CartProvider>
              <Suspense fallback={<div>Loading...</div>}>
                <Header />
                {children}
                <Footer />
              </Suspense>
              <Toaster position="bottom-right" richColors closeButton />
              <PerformanceMonitor />
              <Analytics />
            </CartProvider>
          </WishlistProvider>
        </SearchProvider>
      </body>
    </html>
  )
}
